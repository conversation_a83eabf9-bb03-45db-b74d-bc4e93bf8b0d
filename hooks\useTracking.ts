import { useState, useEffect, useRef } from 'react';
import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';

const LOCATION_TRACKING = 'location-tracking';

interface TrackingState {
  isTracking: boolean;
  currentLocation: {
    latitude: number;
    longitude: number;
  } | null;
  speed: number | null; // in m/s
  distance: number | null; // in meters
  eta: number | null; // in seconds
  errorMsg: string | null;
}

export function useTracking(destination: {
  latitude: number;
  longitude: number;
} | null) {
  const [tracking, setTracking] = useState<TrackingState>({
    isTracking: false,
    currentLocation: null,
    speed: null,
    distance: null,
    eta: null,
    errorMsg: null,
  });

  const destinationRef = useRef(destination);
  const previousLocationRef = useRef<{ latitude: number; longitude: number } | null>(null);

  useEffect(() => {
    destinationRef.current = destination;
  }, [destination]);

  useEffect(() => {
    // Define the task
    TaskManager.defineTask(LOCATION_TRACKING, async ({ data, error }: any) => {
      if (error) {
        console.error('Location tracking task error:', error);
        return;
      }
      if (data) {
        const { locations } = data;
        const location = locations[0];
        
        if (location) {
          const { latitude, longitude, speed } = location.coords;
          
          // Calculate distance to destination
          let distance = null;
          let eta = null;
          
          if (destinationRef.current) {
            distance = calculateDistance(
              latitude,
              longitude,
              destinationRef.current.latitude,
              destinationRef.current.longitude
            );
            
            // Calculate ETA based on current speed
            if (speed > 0) {
              eta = distance / speed; // seconds
            }
          }
          
          // Calculate actual speed if GPS speed is unreliable
          let calculatedSpeed = speed;
          if (previousLocationRef.current && speed === 0) {
            const timeDiff = location.timestamp - (previousLocationRef.current as any).timestamp || 1000;
            const distanceDiff = calculateDistance(
              latitude,
              longitude,
              previousLocationRef.current.latitude,
              previousLocationRef.current.longitude
            );
            calculatedSpeed = distanceDiff / (timeDiff / 1000);
          }
          
          previousLocationRef.current = {
            ...location.coords,
            timestamp: location.timestamp,
          };
          
          // Update tracking state
          setTracking(prev => ({
            ...prev,
            currentLocation: { latitude, longitude },
            speed: calculatedSpeed || 0,
            distance,
            eta,
          }));
        }
      }
    });
    
    return () => {
      stopTracking();
    };
  }, []);

  const startTracking = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        setTracking(prev => ({
          ...prev,
          errorMsg: 'Permission to access location was denied',
        }));
        return false;
      }
      
      await Location.startLocationUpdatesAsync(LOCATION_TRACKING, {
        accuracy: Location.Accuracy.BestForNavigation,
        timeInterval: 5000, // Update every 5 seconds
        distanceInterval: 10, // or 10 meters
        foregroundService: {
          notificationTitle: 'WakeMeGo is tracking your location',
          notificationBody: 'We will alert you when you approach your destination',
        },
      });
      
      setTracking(prev => ({
        ...prev,
        isTracking: true,
        errorMsg: null,
      }));
      
      return true;
    } catch (error) {
      setTracking(prev => ({
        ...prev,
        errorMsg: 'Failed to start tracking',
        isTracking: false,
      }));
      return false;
    }
  };

  const stopTracking = async () => {
    try {
      const isRegistered = await TaskManager.isTaskRegisteredAsync(LOCATION_TRACKING);
      
      if (isRegistered) {
        await Location.stopLocationUpdatesAsync(LOCATION_TRACKING);
      }
      
      setTracking(prev => ({
        ...prev,
        isTracking: false,
      }));
      
      return true;
    } catch (error) {
      setTracking(prev => ({
        ...prev,
        errorMsg: 'Failed to stop tracking',
      }));
      return false;
    }
  };

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ) => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // distance in meters
  };

  return {
    ...tracking,
    startTracking,
    stopTracking,
  };
}