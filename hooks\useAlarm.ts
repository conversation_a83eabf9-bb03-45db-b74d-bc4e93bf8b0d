import { useState, useEffect } from 'react';
import { Vibration } from 'react-native';
import * as Haptics from 'expo-haptics';

interface AlarmState {
  isActive: boolean;
  isSnoozed: boolean;
  volume: number; // 0 to 1
  remainingSnoozeTime: number | null; // in seconds
}

export function useAlarm() {
  const [alarmState, setAlarmState] = useState<AlarmState>({
    isActive: false,
    isSnoozed: false,
    volume: 0,
    remainingSnoozeTime: null,
  });

  const vibrationPattern = [500, 200, 500];
  let volumeInterval: NodeJS.Timeout | null = null;
  let snoozeInterval: NodeJS.Timeout | null = null;

  useEffect(() => {
    return () => {
      // Clean up
      if (volumeInterval) clearInterval(volumeInterval);
      if (snoozeInterval) clearInterval(snoozeInterval);
      Vibration.cancel();
    };
  }, []);

  useEffect(() => {
    if (alarmState.isActive && !alarmState.isSnoozed) {
      // Start vibration
      Vibration.vibrate(vibrationPattern, true);
      
      // Gradually increase volume
      if (volumeInterval) clearInterval(volumeInterval);
      
      let currentVolume = 0;
      volumeInterval = setInterval(() => {
        if (currentVolume < 1) {
          currentVolume = Math.min(currentVolume + 0.1, 1);
          setAlarmState(prev => ({ ...prev, volume: currentVolume }));
        } else {
          if (volumeInterval) clearInterval(volumeInterval);
        }
      }, 1000); // Increase volume every second
    } else {
      // Stop vibration and volume increase
      Vibration.cancel();
      if (volumeInterval) clearInterval(volumeInterval);
    }
  }, [alarmState.isActive, alarmState.isSnoozed]);

  useEffect(() => {
    if (alarmState.isSnoozed && alarmState.remainingSnoozeTime !== null) {
      if (snoozeInterval) clearInterval(snoozeInterval);
      
      snoozeInterval = setInterval(() => {
        setAlarmState(prev => {
          if (prev.remainingSnoozeTime !== null && prev.remainingSnoozeTime > 0) {
            return { ...prev, remainingSnoozeTime: prev.remainingSnoozeTime - 1 };
          } else {
            // Resume alarm when snooze time is up
            return {
              ...prev,
              isSnoozed: false,
              remainingSnoozeTime: null,
              volume: 0, // Start volume from beginning
            };
          }
        });
      }, 1000);
    } else {
      if (snoozeInterval) clearInterval(snoozeInterval);
    }
  }, [alarmState.isSnoozed, alarmState.remainingSnoozeTime]);

  const triggerAlarm = () => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    setAlarmState({
      isActive: true,
      isSnoozed: false,
      volume: 0,
      remainingSnoozeTime: null,
    });
  };

  const stopAlarm = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    
    setAlarmState({
      isActive: false,
      isSnoozed: false,
      volume: 0,
      remainingSnoozeTime: null,
    });
    
    Vibration.cancel();
    if (volumeInterval) clearInterval(volumeInterval);
    if (snoozeInterval) clearInterval(snoozeInterval);
  };

  const snoozeAlarm = (seconds: number = 120) => { // Default 2 minutes
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    setAlarmState({
      isActive: true,
      isSnoozed: true,
      volume: 0,
      remainingSnoozeTime: seconds,
    });
    
    Vibration.cancel();
    if (volumeInterval) clearInterval(volumeInterval);
  };

  return {
    ...alarmState,
    triggerAlarm,
    stopAlarm,
    snoozeAlarm,
  };
}