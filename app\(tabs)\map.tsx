import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, SafeAreaView, Animated } from 'react-native';
import { router } from 'expo-router';
import { Search, Check, X, MapPin } from 'lucide-react-native';
import { theme } from '@/constants/theme';
import LeafletMap from '@/components/LeafletMap';
import GradientBackground from '@/components/GradientBackground';
import CustomButton from '@/components/CustomButton';
import { useLocation } from '@/hooks/useLocation';
import { useDestination } from '@/hooks/useDestination';
import * as Haptics from 'expo-haptics';

export default function MapScreen() {
  const { coords, errorMsg } = useLocation();
  const { setDestination, destination, isLoading, error } = useDestination();
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showResults, setShowResults] = useState(false);

  const mapRef = useRef<any>(null);
  const [mapCenter, setMapCenter] = useState<{latitude: number; longitude: number} | null>(null);
  const pinDropAnim = useRef(new Animated.Value(0)).current;

  // Update selected location if destination exists
  useEffect(() => {
    if (destination && !selectedLocation) {
      setSelectedLocation(destination.coordinates);
      
      // Animate to destination
      if (destination.coordinates) {
        setMapCenter(destination.coordinates);
      }
    }
  }, [destination]);

  const handleMapPress = (event: any) => {
    const { coordinate } = event.nativeEvent;
    setSelectedLocation(coordinate);
    
    // Trigger haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Animate pin drop
    pinDropAnim.setValue(0);
    Animated.spring(pinDropAnim, {
      toValue: 1,
      useNativeDriver: true,
      friction: 5,
    }).start();
  };

  const handleConfirm = async () => {
    if (selectedLocation) {
      const success = await setDestination(selectedLocation);
      if (success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        router.back();
      }
    }
  };

  const handleSearch = async (text: string) => {
    setSearchQuery(text);
    
    if (text.length > 2) {
      // In a real app, you would call a geocoding API here
      // For this example, we'll just show mock results
      setSearchResults([
        { id: '1', name: 'Central Station', address: '123 Main St', coords: { latitude: 37.7749, longitude: -122.4194 } },
        { id: '2', name: 'Downtown Mall', address: '456 Market St', coords: { latitude: 37.7897, longitude: -122.4000 } },
        { id: '3', name: 'City Park', address: '789 Park Ave', coords: { latitude: 37.7695, longitude: -122.4830 } },
      ]);
      setShowResults(true);
    } else {
      setSearchResults([]);
      setShowResults(false);
    }
  };

  const handleSelectSearchResult = (result: any) => {
    setSelectedLocation(result.coords);
    setSearchQuery(result.name);
    setShowResults(false);
    
    // Animate to selected location
    if (mapRef.current) {
      setMapCenter(result.coords);
    }
    
    // Animate pin drop
    pinDropAnim.setValue(0);
    Animated.spring(pinDropAnim, {
      toValue: 1,
      useNativeDriver: true,
      friction: 5,
    }).start();
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const translateY = pinDropAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-50, 0],
  });

  return (
    <GradientBackground>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Search size={20} color={theme.colors.gray} style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search for a location"
                value={searchQuery}
                onChangeText={handleSearch}
                placeholderTextColor={theme.colors.gray}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  style={styles.clearButton}
                  onPress={() => {
                    setSearchQuery('');
                    setShowResults(false);
                  }}
                >
                  <X size={18} color={theme.colors.gray} />
                </TouchableOpacity>
              )}
            </View>
            
            {showResults && searchResults.length > 0 && (
              <View style={styles.resultsContainer}>
                {searchResults.map((result) => (
                  <TouchableOpacity
                    key={result.id}
                    style={styles.resultItem}
                    onPress={() => handleSelectSearchResult(result)}
                  >
                    <MapPin size={16} color={theme.colors.primary} />
                    <View style={styles.resultTextContainer}>
                      <Text style={styles.resultName}>{result.name}</Text>
                      <Text style={styles.resultAddress}>{result.address}</Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
          
          <View style={styles.mapContainer}>
            {coords ? (
              <LeafletMap
                style={styles.map}
                center={mapCenter || coords}
                onPress={(coords) => handleMapPress({ nativeEvent: { coordinate: coords } })}
                markers={[
                  // Current location marker
                  {
                    latitude: coords.latitude,
                    longitude: coords.longitude,
                    title: 'You are here',
                    color: theme.colors.primary,
                  },
                  // Selected location marker
                  ...(selectedLocation ? [{
                    latitude: selectedLocation.latitude,
                    longitude: selectedLocation.longitude,
                    color: theme.colors.primary,
                    isAnimated: true,
                  }] : [])
                ]}
              />
            ) : (
              <View style={styles.mapPlaceholder}>
                <Text style={styles.errorText}>{errorMsg || 'Unable to load map'}</Text>
              </View>
            )}
          </View>
          
          <View style={styles.footer}>
            {selectedLocation && (
              <View style={styles.selectionInfo}>
                <Text style={styles.selectionText}>
                  {isLoading ? 'Getting location info...' : 'Location selected'}
                </Text>
              </View>
            )}
            
            <CustomButton
              title="Confirm Destination"
              onPress={handleConfirm}
              disabled={!selectedLocation || isLoading}
              loading={isLoading}
              size="large"
              style={styles.confirmButton}
            />
            
            {error && (
              <Text style={styles.errorText}>{error}</Text>
            )}
          </View>
        </View>
      </SafeAreaView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  searchContainer: {
    position: 'absolute',
    top: theme.spacing.m,
    left: theme.spacing.m,
    right: theme.spacing.m,
    zIndex: 10,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.large,
    paddingHorizontal: theme.spacing.m,
    height: 50,
    ...theme.shadows.medium,
  },
  searchIcon: {
    marginRight: theme.spacing.s,
  },
  searchInput: {
    flex: 1,
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.black,
  },
  clearButton: {
    padding: theme.spacing.xs,
  },
  resultsContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.large,
    marginTop: theme.spacing.s,
    padding: theme.spacing.s,
    ...theme.shadows.medium,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.s,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.lightGray,
  },
  resultTextContainer: {
    marginLeft: theme.spacing.s,
    flex: 1,
  },
  resultName: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.black,
  },
  resultAddress: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  footer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: theme.spacing.m,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
    ...theme.shadows.medium,
  },
  selectionInfo: {
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  selectionText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.darkGray,
  },
  confirmButton: {
    width: '100%',
  },
  errorText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.error,
    textAlign: 'center',
    marginTop: theme.spacing.s,
  },
});