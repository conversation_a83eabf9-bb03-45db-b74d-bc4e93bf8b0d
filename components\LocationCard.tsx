import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { MapPin } from 'lucide-react-native';
import { theme } from '@/constants/theme';

interface LocationCardProps {
  location: {
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
}

export default function LocationCard({ location }: LocationCardProps) {
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    pulse.start();

    return () => {
      pulse.stop();
    };
  }, [pulseAnim]);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: pulseAnim }],
        },
      ]}
    >
      <View style={styles.iconContainer}>
        <MapPin size={28} color={theme.colors.primary} />
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.title}>Current Location</Text>
        <Text style={styles.address}>{location.address}</Text>
        {location.coordinates && (
          <Text style={styles.coordinates}>
            {location.coordinates.latitude.toFixed(6)}, {location.coordinates.longitude.toFixed(6)}
          </Text>
        )}
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'center',
    width: '80%',
    ...theme.shadows.medium,
  },
  iconContainer: {
    marginRight: theme.spacing.m,
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoContainer: {
    flex: 1,
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.darkGray,
    marginBottom: theme.spacing.xs,
  },
  address: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.black,
    marginBottom: theme.spacing.xs,
  },
  coordinates: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.gray,
  },
});