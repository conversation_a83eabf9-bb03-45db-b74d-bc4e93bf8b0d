import { useState, useEffect } from 'react';
import * as Location from 'expo-location';

interface LocationState {
  coords: {
    latitude: number;
    longitude: number;
    accuracy: number | undefined | null;
  } | null;
  address: string;
  errorMsg: string | null;
  isLoading: boolean;
}

export function useLocation(refreshInterval = 30000) {
  const [location, setLocation] = useState<LocationState>({
    coords: null,
    address: 'Loading location...',
    errorMsg: null,
    isLoading: true,
  });

  const checkLocationServices = async () => {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        setLocation(prev => ({
          ...prev,
          errorMsg: 'Location services are disabled. Please enable them in your device settings.',
          isLoading: false,
        }));
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error checking location services:', error);
      return false;
    }
  };

  const requestPermissions = async () => {
    try {
      // Check current permission status first
      const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
      
      if (existingStatus === 'granted') {
        return true;
      }

      // Request permissions if not already granted
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        setLocation(prev => ({
          ...prev,
          errorMsg: 'Location permission is required. Please enable it in your device settings.',
          isLoading: false,
        }));
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      setLocation(prev => ({
        ...prev,
        errorMsg: 'Failed to request location permissions. Please try again.',
        isLoading: false,
      }));
      return false;
    }
  };

  const getLocationAsync = async () => {
    try {
      setLocation(prev => ({ ...prev, isLoading: true, errorMsg: null }));
      
      // Check if location services are enabled
      const servicesEnabled = await checkLocationServices();
      if (!servicesEnabled) return;

      // Check permissions
      const hasPermission = await requestPermissions();
      if (!hasPermission) return;

      // Wait a bit to ensure location services are fully initialized
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Try to get last known location first
      const lastKnownLocation = await Location.getLastKnownPositionAsync({
        maxAge: 60000, // Accept locations up to 1 minute old
      });

      // Get current position with optimized settings
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 5000,
        mayShowUserSettingsDialog: true,
      });
      
      // Use the most recent location
      const locationToUse = currentLocation || lastKnownLocation;
      
      if (!locationToUse) {
        throw new Error('Could not get current location');
      }

      const { latitude, longitude, accuracy } = locationToUse.coords;
      
      // Get address from coordinates
      const addressResponse = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      
      const addressDetails = addressResponse[0];
      const formattedAddress = addressDetails
        ? `${addressDetails.street || ''} ${addressDetails.name || ''}, ${
            addressDetails.city || ''
          }, ${addressDetails.region || ''}`
        : 'Unknown location';
      
      setLocation({
        coords: {
          latitude,
          longitude,
          accuracy: accuracy || undefined,
        },
        address: formattedAddress,
        errorMsg: null,
        isLoading: false,
      });
      
    } catch (error) {
      console.error('Error getting location:', error);
      let errorMessage = 'Failed to get location. ';
      
      if (error instanceof Error) {
        // Check for specific error messages
        if (error.message.includes('location') || error.message.includes('Location')) {
          errorMessage = 'Could not access your location. Please:';
          errorMessage += '\n1. Check if location services are enabled';
          errorMessage += '\n2. Make sure you have an active internet connection';
          errorMessage += '\n3. Try moving to an area with better GPS signal';
        } else {
          errorMessage += error.message;
        }
      }

      setLocation(prev => ({
        ...prev,
        errorMsg: errorMessage,
        isLoading: false,
      }));
    }
  };

  useEffect(() => {
    let isMounted = true;
    let intervalId: ReturnType<typeof setInterval>;
    
    const initialize = async () => {
      if (isMounted) {
        await getLocationAsync();
        
        // Set up interval for location updates only if first attempt was successful
        if (isMounted && !location.errorMsg) {
          intervalId = setInterval(() => {
            getLocationAsync();
          }, refreshInterval);
        }
      }
    };

    initialize();
    
    // Clean up
    return () => {
      isMounted = false;
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [refreshInterval]);

  return {
    ...location,
    refreshLocation: getLocationAsync,
  };
}