export const theme = {
  colors: {
    primary: '#FF7EB3',
    secondary: '#FF758C',
    white: '#FFFFFF',
    black: '#000000',
    gray: '#9A9A9A',
    lightGray: '#E5E5E5',
    darkGray: '#444444',
    success: '#4CAF50',
    warning: '#FFC107',
    error: '#F44336',
    background: {
      start: '#FF7EB3',
      end: '#FF758C',
    }
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    small: 8,
    medium: 16,
    large: 20,
    round: 999,
  },
  typography: {
    fontFamily: {
      regular: 'Inter-Regular',
      medium: 'Inter-Medium',
      bold: 'Inter-Bold',
    },
    fontSize: {
      xs: 12,
      s: 14,
      m: 16,
      l: 20,
      xl: 24,
      xxl: 32,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      loose: 1.8,
    },
  },
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.15,
      shadowRadius: 10,
      elevation: 5,
    },
  },
};