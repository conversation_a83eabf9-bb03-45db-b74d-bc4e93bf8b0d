import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import { Navigation, AlertTriangle } from 'lucide-react-native';
import LeafletMap from '@/components/LeafletMap';
import { theme } from '@/constants/theme';
import GradientBackground from '@/components/GradientBackground';
import CustomButton from '@/components/CustomButton';
import CircularProgress from '@/components/CircularProgress';
import { useLocation } from '@/hooks/useLocation';
import { useDestination } from '@/hooks/useDestination';
import { useTracking } from '@/hooks/useTracking';
import { useAlarm } from '@/hooks/useAlarm';

const ALARM_TRIGGER_DISTANCE = 500; // meters

export default function TrackingScreen() {
  const { coords } = useLocation(10000); // Update every 10 seconds
  const { destination, calculateRoute } = useDestination();
  const { triggerAlarm } = useAlarm();
  const [routeInfo, setRouteInfo] = useState<{
    distance: number;
    duration: number;
    progress: number;
  } | null>(null);
  const [hasTriggeredAlarm, setHasTriggeredAlarm] = useState(false);

  const tracking = useTracking(
    destination?.coordinates
      ? {
          latitude: destination.coordinates.latitude,
          longitude: destination.coordinates.longitude,
        }
      : null
  );

  useEffect(() => {
    if (!destination) {
      Alert.alert(
        'No Destination Set',
        'Please set a destination before tracking.',
        [
          {
            text: 'Set Destination',
            onPress: () => router.replace('/map'),
          },
        ]
      );
      return;
    }

    // Start tracking when component mounts
    const startTrackingProcess = async () => {
      const success = await tracking.startTracking();
      if (!success) {
        Alert.alert(
          'Tracking Error',
          tracking.errorMsg || 'Failed to start location tracking.'
        );
      }
    };

    startTrackingProcess();

    // Clean up tracking when component unmounts
    return () => {
      tracking.stopTracking();
    };
  }, [destination]);

  useEffect(() => {
    // Calculate route when we have current location and destination
    const updateRouteInfo = async () => {
      if (coords && destination?.coordinates) {
        const updatedDestination = await calculateRoute({
          latitude: coords.latitude,
          longitude: coords.longitude,
        });

        if (updatedDestination?.distance && updatedDestination?.duration) {
          // Calculate progress (1 - current/total)
          const totalDistance = updatedDestination.distance;
          const currentDistance = tracking.distance || totalDistance;
          const progress = Math.max(0, Math.min(1, 1 - currentDistance / totalDistance));

          setRouteInfo({
            distance: currentDistance,
            duration: tracking.eta || updatedDestination.duration,
            progress,
          });
          
          // Check if we should trigger the alarm
          if (
            currentDistance <= ALARM_TRIGGER_DISTANCE &&
            !hasTriggeredAlarm
          ) {
            triggerAlarm();
            setHasTriggeredAlarm(true);
            router.replace('/alarm');
          }
        }
      }
    };

    updateRouteInfo();
  }, [coords, destination, tracking.distance, tracking.eta]);

  const handleStopTracking = () => {
    Alert.alert(
      'Stop Tracking',
      'Are you sure you want to cancel your journey?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Stop',
          style: 'destructive',
          onPress: async () => {
            await tracking.stopTracking();
            router.replace('/');
          },
        },
      ]
    );
  };

  const formatDistance = (meters: number) => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)} km`;
    }
    return `${Math.round(meters)} m`;
  };

  const formatDuration = (seconds: number) => {
    if (seconds >= 3600) {
      return `${Math.floor(seconds / 3600)} h ${Math.floor((seconds % 3600) / 60)} min`;
    }
    if (seconds >= 60) {
      return `${Math.floor(seconds / 60)} min`;
    }
    return `${Math.round(seconds)} sec`;
  };

  const formatSpeed = (metersPerSecond: number | null) => {
    if (metersPerSecond === null) return '-- km/h';
    const kmh = metersPerSecond * 3.6;
    return `${kmh.toFixed(1)} km/h`;
  };

  return (
    <GradientBackground>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Tracking Journey</Text>
            {destination && (
              <Text style={styles.destination}>To: {destination.address}</Text>
            )}
          </View>

          <View style={styles.progressContainer}>
            <CircularProgress
              size={220}
              strokeWidth={12}
              progress={routeInfo?.progress || 0}
            >
              <View style={styles.progressContent}>
                <Text style={styles.distanceValue}>
                  {routeInfo ? formatDistance(routeInfo.distance) : '-- km'}
                </Text>
                <Text style={styles.distanceLabel}>remaining</Text>
              </View>
            </CircularProgress>

            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>ETA</Text>
                <Text style={styles.statValue}>
                  {routeInfo ? formatDuration(routeInfo.duration) : '-- min'}
                </Text>
              </View>
              <View style={styles.separator} />
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Speed</Text>
                <Text style={styles.statValue}>{formatSpeed(tracking.speed)}</Text>
              </View>
            </View>
          </View>

          <View style={styles.mapContainer}>
            {coords && destination?.coordinates && (
              <LeafletMap
                style={styles.map}
                center={{
                  latitude: coords.latitude,
                  longitude: coords.longitude
                }}
                zoom={13}
                markers={[
                  // Current location marker
                  {
                    latitude: coords.latitude,
                    longitude: coords.longitude,
                    title: 'Your location',
                    color: theme.colors.primary
                  },
                  // Destination marker with navigation icon
                  {
                    latitude: destination.coordinates.latitude,
                    longitude: destination.coordinates.longitude,
                    title: destination.address,
                    color: theme.colors.primary,
                  }
                ]}
                onPress={() => {}} // Disable map interaction during tracking
              />
            )}
          </View>

          {tracking.errorMsg && (
            <View style={styles.errorContainer}>
              <AlertTriangle size={20} color={theme.colors.error} />
              <Text style={styles.errorText}>{tracking.errorMsg}</Text>
            </View>
          )}

          <View style={styles.footer}>
            <CustomButton
              title="Cancel Journey"
              onPress={handleStopTracking}
              variant="secondary"
              style={styles.cancelButton}
            />
          </View>
        </View>
      </SafeAreaView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: theme.spacing.m,
  },
  header: {
    alignItems: 'center',
    marginVertical: theme.spacing.l,
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.white,
    marginBottom: theme.spacing.xs,
  },
  destination: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    opacity: 0.8,
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.l,
  },
  progressContent: {
    alignItems: 'center',
  },
  distanceValue: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.primary,
  },
  distanceLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.darkGray,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.m,
    marginTop: theme.spacing.l,
    width: '90%',
    ...theme.shadows.medium,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray,
    marginBottom: theme.spacing.xs,
  },
  statValue: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.darkGray,
  },
  separator: {
    width: 1,
    height: '100%',
    backgroundColor: theme.colors.lightGray,
  },
  mapContainer: {
    flex: 1,
    borderRadius: theme.borderRadius.large,
    overflow: 'hidden',
    ...theme.shadows.medium,
  },
  map: {
    flex: 1,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.m,
    marginTop: theme.spacing.m,
  },
  errorText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.error,
    marginLeft: theme.spacing.s,
  },
  footer: {
    marginTop: theme.spacing.l,
    marginBottom: theme.spacing.m,
  },
  cancelButton: {
    width: '100%',
  },
});