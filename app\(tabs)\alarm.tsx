import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, SafeAreaView, Animated, Easing } from 'react-native';
import { router } from 'expo-router';
import { Bell } from 'lucide-react-native';
import { theme } from '@/constants/theme';
import GradientBackground from '@/components/GradientBackground';
import CustomButton from '@/components/CustomButton';
import { useAlarm } from '@/hooks/useAlarm';

export default function AlarmScreen() {
  const { isActive, isSnoozed, remainingSnoozeTime, triggerAlarm, stopAlarm, snoozeAlarm } = useAlarm();
  
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const backgroundAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // If not already active, trigger alarm
    if (!isActive) {
      triggerAlarm();
    }

    // Start animations
    startAnimations();

    return () => {
      // Clean up animations
      pulseAnim.stopAnimation();
      rotateAnim.stopAnimation();
      backgroundAnim.stopAnimation();
    };
  }, []);

  const startAnimations = () => {
    // Pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 500,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 500,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Rotate animation
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();

    // Background animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(backgroundAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(backgroundAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    ).start();
  };

  const handleAwake = () => {
    stopAlarm();
    router.replace('/');
  };

  const handleSnooze = () => {
    snoozeAlarm(120); // 2 minutes
  };

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const backgroundColor = backgroundAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['rgba(255, 126, 179, 0.8)', 'rgba(255, 117, 140, 0.8)'],
  });

  return (
    <GradientBackground>
      <Animated.View
        style={[
          styles.overlay,
          {
            backgroundColor,
          },
        ]}
      />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <Text style={styles.title}>Wake Up!</Text>
            <Text style={styles.subtitle}>You're approaching your destination</Text>

            <View style={styles.iconContainer}>
              <Animated.View
                style={[
                  styles.animatedIcon,
                  {
                    transform: [{ scale: pulseAnim }, { rotate }],
                  },
                ]}
              >
                <Bell size={100} color={theme.colors.white} />
              </Animated.View>
            </View>

            {isSnoozed ? (
              <View style={styles.snoozeContainer}>
                <Text style={styles.snoozeText}>
                  Snoozed for {Math.floor(remainingSnoozeTime! / 60)}:
                  {(remainingSnoozeTime! % 60).toString().padStart(2, '0')}
                </Text>
              </View>
            ) : (
              <View style={styles.messageContainer}>
                <Text style={styles.message}>Time to wake up!</Text>
                <Text style={styles.submessage}>You're getting close to your destination</Text>
              </View>
            )}
          </View>

          <View style={styles.buttonContainer}>
            <CustomButton
              title="I'm Awake"
              onPress={handleAwake}
              size="large"
              style={styles.awakeButton}
            />
            {!isSnoozed && (
              <CustomButton
                title="Snooze (2 min)"
                onPress={handleSnooze}
                variant="secondary"
                style={styles.snoozeButton}
              />
            )}
          </View>
        </View>
      </SafeAreaView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    padding: theme.spacing.l,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl * 1.5,
    color: theme.colors.white,
    marginBottom: theme.spacing.m,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.white,
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
    opacity: 0.9,
  },
  iconContainer: {
    marginBottom: theme.spacing.xl,
    height: 160,
    alignItems: 'center',
    justifyContent: 'center',
  },
  animatedIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  snoozeContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.m,
    alignItems: 'center',
    justifyContent: 'center',
  },
  snoozeText: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.white,
  },
  messageContainer: {
    alignItems: 'center',
  },
  message: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.white,
    marginBottom: theme.spacing.s,
  },
  submessage: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    opacity: 0.8,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: theme.spacing.xl,
  },
  awakeButton: {
    width: '100%',
    minHeight: 80,
    marginBottom: theme.spacing.m,
  },
  snoozeButton: {
    width: '100%',
  },
});