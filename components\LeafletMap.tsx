import React from 'react';
import { View, StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';
import { theme } from '@/constants/theme';

interface Marker {
  latitude: number;
  longitude: number;
  title?: string;
  color?: string;
  isAnimated?: boolean;
}

interface LeafletMapProps {
  style?: any;
  center: {
    latitude: number;
    longitude: number;
  };
  zoom?: number;
  markers?: Marker[];
  onPress?: (coords: { latitude: number; longitude: number }) => void;
}

const LeafletMap: React.FC<LeafletMapProps> = ({
  style,
  center,
  zoom = 15,
  markers = [],
  onPress,
}) => {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <style>
          body { margin: 0; }
          #map { width: 100vw; height: 100vh; }
          .animated-marker {
            animation: bounce 0.5s ease infinite;
          }
          @keyframes bounce {
            0% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0); }
          }
        </style>
      </head>
      <body>
        <div id="map"></div>
        <script>
          const map = L.map('map').setView([${center.latitude}, ${center.longitude}], ${zoom});
          L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
          }).addTo(map);

          map.on('click', function(e) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'click',
              lat: e.latlng.lat,
              lng: e.latlng.lng
            }));
          });

          // Add markers
          ${markers.map((marker) => `
            const icon${marker.isAnimated ? 'Animated' : ''} = L.divIcon({
              html: \`<div class="${marker.isAnimated ? 'animated-marker' : ''}" style="
                width: 20px;
                height: 20px;
                background-color: ${marker.color || theme.colors.primary};
                border-radius: 50%;
                border: 3px solid white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
              "></div>\`,
              className: '',
              iconSize: [26, 26],
              iconAnchor: [13, 13]
            });
            L.marker([${marker.latitude}, ${marker.longitude}], {
              icon: icon${marker.isAnimated ? 'Animated' : ''}
            })
            .addTo(map)${marker.title ? `.bindPopup("${marker.title}")` : ''};
          `).join('\n')}
        </script>
      </body>
    </html>
  `;

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'click' && onPress) {
        onPress({
          latitude: data.lat,
          longitude: data.lng,
        });
      }
    } catch (error) {
      console.error('Failed to parse WebView message:', error);
    }
  };

  return (
    <View style={[styles.container, style]}>
      <WebView
        source={{ html: htmlContent }}
        style={styles.webview}
        onMessage={handleMessage}
        scrollEnabled={false}
        bounces={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  webview: {
    flex: 1,
  },
});

export default LeafletMap;