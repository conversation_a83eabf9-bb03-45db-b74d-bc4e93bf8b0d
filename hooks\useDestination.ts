import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Destination {
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  distance?: number; // in meters
  duration?: number; // in seconds
}

const DESTINATION_STORAGE_KEY = '@destination';

export function useDestination() {
  const [destination, setDestination] = useState<Destination | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Start with true since we're loading on mount
  const [error, setError] = useState<string | null>(null);

  // Load saved destination on mount
  useEffect(() => {
    loadSavedDestination();
  }, []);

  const loadSavedDestination = async () => {
    try {
      const savedDestination = await AsyncStorage.getItem(DESTINATION_STORAGE_KEY);
      if (savedDestination) {
        setDestination(JSON.parse(savedDestination));
      }
    } catch (error) {
      console.error('Failed to load saved destination:', error);
      setError('Failed to load destination');
    } finally {
      setIsLoading(false);
    }
  };

  const setNewDestination = async (coords: { latitude: number; longitude: number }) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Get address from coordinates
      const addressResponse = await Location.reverseGeocodeAsync(coords);
      
      const addressDetails = addressResponse[0];
      const formattedAddress = addressDetails
        ? `${addressDetails.street || ''} ${addressDetails.name || ''}, ${
            addressDetails.city || ''
          }, ${addressDetails.region || ''}`
        : 'Selected location';
      
      // Create new destination object
      const newDestination: Destination = {
        address: formattedAddress,
        coordinates: coords,
        // These would come from an API in a real app
        distance: 0,
        duration: 0,
      };

      // Save to AsyncStorage
      await AsyncStorage.setItem(DESTINATION_STORAGE_KEY, JSON.stringify(newDestination));
      
      // Update state
      setDestination(newDestination);
      return true;
    } catch (error) {
      setError('Failed to set destination. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const clearDestination = async () => {
    try {
      setIsLoading(true);
      await AsyncStorage.removeItem(DESTINATION_STORAGE_KEY);
      setDestination(null);
    } catch (error) {
      console.error('Failed to clear destination:', error);
      setError('Failed to clear destination');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRoute = async (
    startCoords: { latitude: number; longitude: number }
  ) => {
    if (!destination) return null;
    
    try {
      setIsLoading(true);
      
      // In a real app, you would call a routing API here
      // For this example, we'll use a mock calculation
      const distance = calculateDistance(
        startCoords.latitude,
        startCoords.longitude,
        destination.coordinates.latitude,
        destination.coordinates.longitude
      );
      
      // Estimate duration (assuming average speed of 50 km/h)
      const duration = (distance / 50000) * 3600; // seconds
      
      const updatedDestination = {
        ...destination,
        distance,
        duration,
      };
      
      // Update storage with new distance and duration
      await AsyncStorage.setItem(DESTINATION_STORAGE_KEY, JSON.stringify(updatedDestination));
      
      setDestination(updatedDestination);
      return updatedDestination;
    } catch (error) {
      setError('Failed to calculate route. Please try again.');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ) => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // distance in meters
  };

  return {
    destination,
    isLoading,
    error,
    setDestination: setNewDestination,
    clearDestination,
    calculateRoute,
  };
}