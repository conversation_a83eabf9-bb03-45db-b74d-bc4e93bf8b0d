import React, { useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, ActivityIndicator } from 'react-native';
import { useLocation } from '@/hooks/useLocation';
import { useDestination } from '@/hooks/useDestination';
import { router } from 'expo-router';
import { theme } from '@/constants/theme';
import GradientBackground from '@/components/GradientBackground';
import LocationCard from '@/components/LocationCard';
import CustomButton from '@/components/CustomButton';

export default function HomeScreen() {
  const { coords, address, errorMsg, isLoading: locationLoading, refreshLocation } = useLocation();
  const { destination, isLoading: destinationLoading } = useDestination();

  const handleSetDestination = useCallback(() => {
    router.push('/map');
  }, []);

  const handleStartJourney = useCallback(() => {
    if (destination) {
      router.push('/tracking');
    } else {
      router.push('/map');
    }
  }, [destination]);

  return (
    <GradientBackground>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView contentContainerStyle={styles.scrollView}>
          <View style={styles.container}>
            <View style={styles.header}>
              <Text style={styles.title}>WakeMeGo</Text>
              <Text style={styles.subtitle}>Never miss your stop again</Text>
            </View>

            <View style={styles.cardContainer}>
              {locationLoading ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>Getting your location...</Text>
                </View>
              ) : errorMsg ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{errorMsg}</Text>
                  <CustomButton title="Retry" onPress={refreshLocation} variant="secondary" size="small" />
                </View>
              ) : (
                coords && (
                  <LocationCard
                    location={{
                      address,
                      coordinates: {
                        latitude: coords.latitude,
                        longitude: coords.longitude,
                      },
                    }}
                  />
                )
              )}
            </View>

            <View style={styles.destinationContainer}>
              <Text style={styles.destinationTitle}>Destination</Text>
              {destinationLoading ? (
                <View style={styles.loadingDestination}>
                  <ActivityIndicator color={theme.colors.white} />
                  <Text style={styles.loadingText}>Loading destination...</Text>
                </View>
              ) : destination ? (
                <View style={styles.selectedDestination}>
                  <Text style={styles.destinationAddress}>{destination.address}</Text>
                  {destination.distance && destination.duration && (
                    <Text style={styles.destinationDetails}>
                      {(destination.distance / 1000).toFixed(1)} km • Approx.{' '}
                      {Math.ceil(destination.duration / 60)} min
                    </Text>
                  )}
                </View>
              ) : (
                <Text style={styles.noDestination}>No destination set</Text>
              )}
            </View>

            <View style={styles.buttonContainer}>
              <CustomButton
                title="Set Destination"
                onPress={handleSetDestination}
                variant="secondary"
                style={styles.button}
              />
              <CustomButton
                title="Start Journey"
                onPress={handleStartJourney}
                disabled={!destination || destinationLoading}
                style={styles.button}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.m,
  },
  header: {
    alignItems: 'center',
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.white,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    opacity: 0.8,
  },
  cardContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: theme.spacing.l,
  },
  loadingContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.l,
    width: '80%',
    alignItems: 'center',
    ...theme.shadows.medium,
  },
  loadingText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginTop: theme.spacing.s,
  },
  errorContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.l,
    width: '80%',
    alignItems: 'center',
    ...theme.shadows.medium,
  },
  errorText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.error,
    marginBottom: theme.spacing.m,
    textAlign: 'center',
  },
  destinationContainer: {
    width: '80%',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.m,
    marginBottom: theme.spacing.xl,
  },
  destinationTitle: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginBottom: theme.spacing.s,
  },
  loadingDestination: {
    alignItems: 'center',
    padding: theme.spacing.m,
  },
  selectedDestination: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.m,
  },
  destinationAddress: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginBottom: theme.spacing.xs,
  },
  destinationDetails: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.white,
    opacity: 0.8,
  },
  noDestination: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    opacity: 0.6,
    padding: theme.spacing.m,
  },
  buttonContainer: {
    width: '80%',
    marginBottom: theme.spacing.xl,
  },
  button: {
    width: '100%',
    marginBottom: theme.spacing.m,
  },
});